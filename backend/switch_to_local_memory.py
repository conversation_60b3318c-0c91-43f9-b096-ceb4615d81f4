#!/usr/bin/env python3
"""
切换到本地记忆系统
检查配置并更新相关服务
"""

import os
import sys
import logging
from datetime import datetime

# 添加backend目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_dependencies():
    """检查依赖包"""
    logger.info("🔍 检查依赖包...")
    
    required_packages = [
        ('sqlalchemy', 'SQLAlchemy ORM'),
        ('psycopg2', 'PostgreSQL驱动'),
        ('pydantic', 'Pydantic数据验证'),
    ]
    
    missing_packages = []
    
    for package, description in required_packages:
        try:
            __import__(package)
            logger.info(f"✅ {description}: {package}")
        except ImportError:
            logger.error(f"❌ {description}: {package} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        logger.error("❌ 缺少必要的依赖包，请运行:")
        logger.error(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True


def check_database_config():
    """检查数据库配置"""
    logger.info("🔍 检查数据库配置...")
    
    database_url = Config.MEMORY_CONFIG.get('database_url')
    if not database_url:
        logger.error("❌ 未配置数据库URL")
        logger.info("请在环境变量中设置: MEMORY_DATABASE_URL=postgresql://user:password@localhost:5432/virtual_memory")
        return False
    
    logger.info(f"✅ 数据库URL: {database_url}")
    
    # 检查PostgreSQL连接
    try:
        import psycopg2
        from urllib.parse import urlparse
        
        parsed = urlparse(database_url)
        conn = psycopg2.connect(
            host=parsed.hostname or 'localhost',
            port=parsed.port or 5432,
            database=parsed.path.lstrip('/') if parsed.path else 'postgres',
            user=parsed.username or 'postgres',
            password=parsed.password or ''
        )
        conn.close()
        logger.info("✅ PostgreSQL连接成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ PostgreSQL连接失败: {e}")
        logger.info("请确保：")
        logger.info("1. PostgreSQL服务已启动")
        logger.info("2. 数据库URL配置正确")
        logger.info("3. 数据库已创建")
        return False


def test_local_memory_service():
    """测试本地记忆服务"""
    logger.info("🧪 测试本地记忆服务...")
    
    try:
        from services.local_memory_service import LocalMemoryService
        
        # 创建服务实例
        memory_service = LocalMemoryService()
        
        # 测试连接
        if not memory_service.ping():
            logger.error("❌ 本地记忆服务连接失败")
            return False
        
        logger.info("✅ 本地记忆服务连接成功")
        
        # 测试基本功能
        test_user_id = "test_switch_user"
        test_messages = [
            {"role": "user", "content": "测试消息", "timestamp": datetime.now().isoformat()}
        ]
        
        # 插入测试数据
        blob_id = memory_service.insert_chat_data(test_user_id, test_messages)
        logger.info(f"✅ 测试数据插入成功: {blob_id}")
        
        # 获取用户画像
        profile = memory_service.get_user_profile(test_user_id)
        logger.info(f"✅ 用户画像获取成功: {len(profile) if profile else 0} 条")
        
        # 刷新记忆
        flush_result = memory_service.flush_user_memory(test_user_id)
        logger.info(f"✅ 记忆刷新: {'成功' if flush_result else '失败'}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 本地记忆服务测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def test_local_memory_client():
    """测试本地记忆客户端"""
    logger.info("🧪 测试本地记忆客户端...")
    
    try:
        from clients.local_memory_client import LocalMemoryClient, ChatBlob
        
        # 创建客户端
        client = LocalMemoryClient()
        
        # 测试连接
        if not client.ping():
            logger.error("❌ 本地记忆客户端连接失败")
            return False
        
        logger.info("✅ 本地记忆客户端连接成功")
        
        # 测试用户操作
        test_user = client.get_or_create_user("test_client_user")
        logger.info(f"✅ 用户创建成功: {test_user.user_id}")
        
        # 测试数据插入
        chat_blob = ChatBlob(messages=[
            {"role": "user", "content": "客户端测试消息"}
        ])
        blob_id = test_user.insert(chat_blob)
        logger.info(f"✅ 数据插入成功: {blob_id}")
        
        # 测试画像获取
        profile = test_user.get_profile()
        logger.info(f"✅ 画像获取成功: {len(profile) if profile else 0} 条")
        
        # 测试记忆刷新
        flush_result = test_user.flush()
        logger.info(f"✅ 记忆刷新: {'成功' if flush_result else '失败'}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 本地记忆客户端测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def test_memory_managers():
    """测试记忆管理器"""
    logger.info("🧪 测试记忆管理器...")
    
    try:
        from services.local_memory_manager import LocalMemoryManager
        from services.local_persona_memory_manager import LocalPersonaMemoryManager
        
        # 测试用户记忆管理器
        memory_manager = LocalMemoryManager()
        if not memory_manager.ping():
            logger.error("❌ 用户记忆管理器连接失败")
            return False
        
        logger.info("✅ 用户记忆管理器连接成功")
        
        # 测试虚拟人记忆管理器
        persona_manager = LocalPersonaMemoryManager()
        stats = persona_manager.get_memory_stats()
        logger.info(f"✅ 虚拟人记忆管理器: {stats.get('total_memories', 0)} 条记忆")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 记忆管理器测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def show_migration_status():
    """显示迁移状态"""
    logger.info("📊 显示迁移状态...")
    
    print("\n" + "=" * 60)
    print("📋 本地记忆系统状态")
    print("=" * 60)
    
    # 配置信息
    print(f"数据库URL: {Config.MEMORY_CONFIG.get('database_url')}")
    print(f"项目ID: {Config.MEMORY_CONFIG.get('project_id')}")
    print(f"最大Token: {Config.MEMORY_CONFIG.get('max_token_size')}")
    print(f"自动刷新: {Config.MEMORY_CONFIG.get('auto_flush')}")
    
    # 统计信息
    try:
        from clients.local_memory_client import LocalMemoryClient
        client = LocalMemoryClient()
        stats = client.get_usage_stats()
        
        print(f"\n📊 数据库统计:")
        print(f"用户数: {stats.get('users', 0)}")
        print(f"数据块数: {stats.get('blobs', 0)}")
        print(f"画像数: {stats.get('profiles', 0)}")
        
    except Exception as e:
        print(f"❌ 获取统计信息失败: {e}")
    
    print("=" * 60)


def main():
    """主函数"""
    print("=" * 60)
    print("🔄 切换到本地记忆系统")
    print("=" * 60)
    
    # 1. 检查依赖
    if not check_dependencies():
        logger.error("❌ 依赖检查失败")
        return False
    
    # 2. 检查数据库配置
    if not check_database_config():
        logger.error("❌ 数据库配置检查失败")
        return False
    
    # 3. 测试本地记忆服务
    if not test_local_memory_service():
        logger.error("❌ 本地记忆服务测试失败")
        return False
    
    # 4. 测试本地记忆客户端
    if not test_local_memory_client():
        logger.error("❌ 本地记忆客户端测试失败")
        return False
    
    # 5. 测试记忆管理器
    if not test_memory_managers():
        logger.error("❌ 记忆管理器测试失败")
        return False
    
    # 6. 显示状态
    show_migration_status()
    
    print("\n🎉 本地记忆系统切换成功!")
    print("✅ 所有组件测试通过")
    print("📋 现在可以启动虚拟人系统了")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
