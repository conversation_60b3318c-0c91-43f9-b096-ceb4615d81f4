"""
本地记忆客户端
替换memobase客户端，提供兼容的API接口
"""

import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from services.local_memory_service import LocalMemoryService
from config import Config

logger = logging.getLogger(__name__)


@dataclass
class LocalUser:
    """本地用户对象，兼容memobase User接口"""
    user_id: str
    memory_service: LocalMemoryService
    
    def insert(self, blob_data) -> str:
        """插入数据"""
        if hasattr(blob_data, 'to_request'):
            # 兼容memobase的Blob对象
            data = blob_data.to_request()
        elif hasattr(blob_data, 'messages'):
            # ChatBlob类型
            data = {"messages": blob_data.messages}
        else:
            # 直接传入的数据
            data = blob_data
        
        # 提取消息数据
        messages = data.get('messages', [])
        return self.memory_service.insert_chat_data(self.user_id, messages)
    
    def flush(self) -> bool:
        """刷新记忆"""
        return self.memory_service.flush_user_memory(self.user_id)
    
    def get_profile(self, need_json: bool = True):
        """获取用户画像"""
        return self.memory_service.get_user_profile(self.user_id, need_json)
    
    def get_context(self, max_token_size: int = 500, prefer_topics: List[str] = None, 
                   chats: List[Dict] = None, optimize_tokens: bool = False) -> str:
        """获取用户上下文"""
        return self.memory_service.get_user_context(
            self.user_id, max_token_size, prefer_topics, chats, optimize_tokens
        )


class LocalMemoryClient:
    """本地记忆客户端，兼容memobase客户端接口"""
    
    def __init__(self, database_url: str = None):
        """初始化本地记忆客户端"""
        self.memory_service = LocalMemoryService(database_url)
        self.config = Config.MEMOBASE_CONFIG
        
        logger.info("✅ 本地记忆客户端初始化成功")
    
    def get_or_create_user(self, user_id: str) -> LocalUser:
        """获取或创建用户"""
        try:
            # 确保用户存在于数据库中
            user = self.memory_service.db_manager.create_user(
                user_id=user_id,
                project_id=self.memory_service.project_id
            )
            
            return LocalUser(user_id=user_id, memory_service=self.memory_service)
            
        except Exception as e:
            logger.error(f"💥 获取或创建用户失败: {e}")
            raise
    
    def insert_chat_data(self, user: LocalUser, messages: List[Dict]) -> str:
        """插入聊天数据"""
        return user.insert({"messages": messages})
    
    def get_user_profile(self, user: LocalUser, need_json: bool = True):
        """获取用户画像"""
        return user.get_profile(need_json)
    
    def get_user_context(self, user: LocalUser, max_token_size: int = 500, 
                        prefer_topics: List[str] = None, chats: List[Dict] = None,
                        optimize_tokens: bool = False) -> str:
        """获取用户上下文"""
        return user.get_context(max_token_size, prefer_topics, chats, optimize_tokens)
    
    def flush_user_memory(self, user: LocalUser) -> bool:
        """刷新用户记忆"""
        return user.flush()
    
    def ping(self) -> bool:
        """测试连接"""
        return self.memory_service.ping()
    
    def get_usage_stats(self) -> Dict:
        """获取使用统计（模拟）"""
        try:
            with self.memory_service.db_manager.get_session() as session:
                from models.memory_models import User, MemoryBlob, UserProfile
                
                user_count = session.query(User).count()
                blob_count = session.query(MemoryBlob).count()
                profile_count = session.query(UserProfile).count()
                
                return {
                    'users': user_count,
                    'blobs': blob_count,
                    'profiles': profile_count,
                    'provider': 'local_memory'
                }
        except Exception as e:
            logger.error(f"💥 获取使用统计失败: {e}")
            return {'error': str(e)}


# 兼容性类，模拟memobase的ChatBlob
class ChatBlob:
    """聊天数据块，兼容memobase接口"""
    
    def __init__(self, messages: List[Dict]):
        self.messages = messages
    
    def to_request(self) -> Dict:
        """转换为请求格式"""
        return {"messages": self.messages}


# 工厂函数，用于替换原有的memobase导入
def create_local_memory_client() -> LocalMemoryClient:
    """创建本地记忆客户端"""
    return LocalMemoryClient()


# 兼容性导出
MemoBaseClient = LocalMemoryClient  # 别名兼容
User = LocalUser  # 别名兼容
