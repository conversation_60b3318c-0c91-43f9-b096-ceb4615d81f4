#!/usr/bin/env python3
"""
测试新的记忆系统
"""

import os
import sys
import logging

# 添加backend目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置环境变量
os.environ['MEMORY_DATABASE_URL'] = 'postgresql://postgres:123456@localhost:35432/virtual_memory'

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_database_connection():
    """测试数据库连接"""
    try:
        from models.memory import get_memory_db
        
        db = get_memory_db()
        logger.info("✅ 数据库连接成功")
        
        # 测试创建用户
        user = db.create_user('test_user_new', '新测试用户')
        logger.info(f"✅ 创建用户成功: {user['user_id']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def test_embedding_service():
    """测试embedding服务"""
    try:
        from services.embedding_service import get_embedding_service
        
        embedding_service = get_embedding_service()
        logger.info("✅ Embedding服务初始化成功")
        
        # 测试获取embedding（使用fallback）
        embedding = embedding_service.get_embedding("测试文本")
        if embedding:
            logger.info(f"✅ 获取embedding成功: 维度 {len(embedding)}")
        else:
            logger.warning("⚠️ 获取embedding失败，使用fallback")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Embedding服务测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def test_memory_analyzer():
    """测试记忆分析器"""
    try:
        from services.memory_analyzer import get_memory_analyzer
        
        analyzer = get_memory_analyzer()
        logger.info("✅ 记忆分析器初始化成功")
        
        # 测试分析文本
        analysis = analyzer.analyze_text("我喜欢画画和音乐，这让我很开心")
        logger.info(f"✅ 文本分析成功: {len(analysis.get('profile_deltas', []))} 个画像")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 记忆分析器测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def test_memory_service():
    """测试记忆服务"""
    try:
        from services.memory_service import get_memory_service
        
        memory_service = get_memory_service()
        logger.info("✅ 记忆服务初始化成功")
        
        # 测试提取记忆
        memories = memory_service.extract_memories_from_text('test_user_service', '我是一个喜欢画画的人')
        logger.info(f"✅ 记忆提取成功: {len(memories)} 条")
        
        # 测试获取相关记忆
        relevant = memory_service.get_relevant_memories('test_user_service', '艺术相关')
        logger.info(f"✅ 获取相关记忆成功: {len(relevant)} 条")
        
        # 测试虚拟人记忆初始化
        success = memory_service.init_persona_memories()
        logger.info(f"✅ 虚拟人记忆初始化: {'成功' if success else '失败'}")
        
        # 测试获取虚拟人上下文
        context = memory_service.get_persona_context()
        logger.info(f"✅ 获取虚拟人上下文: {len(context)} 字符")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 记忆服务测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def test_vector_search():
    """测试向量搜索功能"""
    try:
        from models.memory import get_memory_db
        
        db = get_memory_db()
        
        # 添加测试数据
        test_embedding1 = [0.1, 0.2, 0.3, 0.4, 0.5]
        test_embedding2 = [0.2, 0.3, 0.4, 0.5, 0.6]
        
        profile_id1 = db.add_user_profile(
            user_id='test_vector_user',
            content='我喜欢画画',
            attributes={'topic': 'interests', 'confidence': 0.9},
            embedding=test_embedding1
        )
        
        profile_id2 = db.add_user_profile(
            user_id='test_vector_user',
            content='我喜欢音乐',
            attributes={'topic': 'interests', 'confidence': 0.8},
            embedding=test_embedding2
        )
        
        logger.info(f"✅ 添加测试画像: {profile_id1}, {profile_id2}")
        
        # 测试向量搜索
        query_embedding = [0.15, 0.25, 0.35, 0.45, 0.55]
        similar_profiles = db.search_similar_profiles(
            user_id='test_vector_user',
            query_embedding=query_embedding,
            limit=5,
            threshold=0.0  # 降低阈值以便测试
        )
        
        logger.info(f"✅ 向量搜索成功: {len(similar_profiles)} 条结果")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 向量搜索测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def main():
    """主测试函数"""
    logger.info("🚀 开始测试新的记忆系统")
    
    tests = [
        ("数据库连接", test_database_connection),
        ("Embedding服务", test_embedding_service),
        ("记忆分析器", test_memory_analyzer),
        ("记忆服务", test_memory_service),
        ("向量搜索", test_vector_search),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 测试: {test_name}")
        try:
            if test_func():
                logger.info(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"💥 {test_name} 测试异常: {e}")
    
    logger.info(f"\n🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过! 记忆系统运行正常")
        return True
    else:
        logger.error("⚠️ 部分测试失败，请检查配置")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
