#!/usr/bin/env python3
"""
初始化本地记忆数据库
创建PostgreSQL数据库和表结构
"""

import os
import sys
import logging
from datetime import datetime

# 添加backend目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.memory_models import init_database, DatabaseManager
from config import Config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_postgresql_connection(database_url: str) -> bool:
    """检查PostgreSQL连接"""
    try:
        import psycopg2
        from urllib.parse import urlparse
        
        # 解析数据库URL
        parsed = urlparse(database_url)
        
        # 尝试连接
        conn = psycopg2.connect(
            host=parsed.hostname or 'localhost',
            port=parsed.port or 5432,
            database=parsed.path.lstrip('/') if parsed.path else 'postgres',
            user=parsed.username or 'postgres',
            password=parsed.password or ''
        )
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"PostgreSQL连接失败: {e}")
        return False


def create_database_if_not_exists(database_url: str) -> bool:
    """如果数据库不存在则创建"""
    try:
        import psycopg2
        from urllib.parse import urlparse
        
        parsed = urlparse(database_url)
        db_name = parsed.path.lstrip('/') if parsed.path else 'virtual_memory'
        
        # 连接到postgres数据库来创建目标数据库
        admin_url = database_url.replace(f'/{db_name}', '/postgres')
        
        conn = psycopg2.connect(admin_url)
        conn.autocommit = True
        cursor = conn.cursor()
        
        # 检查数据库是否存在
        cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", (db_name,))
        exists = cursor.fetchone()
        
        if not exists:
            cursor.execute(f'CREATE DATABASE "{db_name}"')
            logger.info(f"✅ 创建数据库: {db_name}")
        else:
            logger.info(f"📁 数据库已存在: {db_name}")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"创建数据库失败: {e}")
        return False


def init_memory_database():
    """初始化记忆数据库"""
    print("=" * 60)
    print("🗃️  虚拟人本地记忆数据库初始化工具")
    print("=" * 60)
    
    # 获取数据库配置
    database_url = Config.MEMORY_CONFIG.get('database_url')
    if not database_url:
        logger.error("❌ 未配置数据库URL，请在环境变量中设置 MEMORY_DATABASE_URL")
        return False
    
    logger.info(f"📁 数据库URL: {database_url}")
    
    # 检查PostgreSQL依赖
    try:
        import psycopg2
        logger.info("✅ PostgreSQL驱动已安装")
    except ImportError:
        logger.error("❌ PostgreSQL驱动未安装，请运行: pip install psycopg2-binary")
        return False
    
    # 创建数据库（如果不存在）
    if not create_database_if_not_exists(database_url):
        logger.error("❌ 创建数据库失败")
        return False
    
    # 检查数据库连接
    if not check_postgresql_connection(database_url):
        logger.error("❌ 无法连接到PostgreSQL数据库")
        logger.info("请确保：")
        logger.info("1. PostgreSQL服务已启动")
        logger.info("2. 数据库URL配置正确")
        logger.info("3. 用户有足够的权限")
        return False
    
    logger.info("✅ PostgreSQL连接成功")
    
    # 初始化数据库表结构
    try:
        logger.info("🔧 初始化数据库表结构...")
        init_database(database_url)
        logger.info("✅ 数据库表结构创建成功")
    except Exception as e:
        logger.error(f"❌ 初始化数据库表结构失败: {e}")
        return False
    
    # 测试数据库管理器
    try:
        logger.info("🧪 测试数据库管理器...")
        db_manager = DatabaseManager(database_url)
        
        # 创建测试用户
        test_user = db_manager.create_user(
            user_id="test_user_init",
            project_id="test_project",
            additional_fields={"test": True}
        )
        logger.info(f"✅ 测试用户创建成功: {test_user.user_id}")
        
        # 插入测试数据
        blob_id = db_manager.insert_blob(
            user_id="test_user_init",
            blob_type="chat",
            blob_data={"messages": [{"role": "user", "content": "测试消息"}]},
            project_id="test_project"
        )
        logger.info(f"✅ 测试数据插入成功: {blob_id}")
        
        # 获取统计信息
        with db_manager.get_session() as session:
            from models.memory_models import User, MemoryBlob, UserProfile
            
            user_count = session.query(User).count()
            blob_count = session.query(MemoryBlob).count()
            profile_count = session.query(UserProfile).count()
            
            logger.info(f"📊 数据库统计: 用户={user_count}, 数据块={blob_count}, 画像={profile_count}")
        
    except Exception as e:
        logger.error(f"❌ 数据库管理器测试失败: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 本地记忆数据库初始化完成!")
    print("=" * 60)
    print("📋 接下来可以：")
    print("1. 启动虚拟人系统: python start.py")
    print("2. 查看数据库内容: 使用PostgreSQL客户端连接")
    print("3. 配置环境变量: 确保MEMORY_DATABASE_URL正确设置")
    print("=" * 60)
    
    return True


def show_database_info():
    """显示数据库信息"""
    database_url = Config.MEMORY_CONFIG.get('database_url')
    print(f"数据库URL: {database_url}")
    print(f"项目ID: {Config.MEMORY_CONFIG.get('project_id')}")
    print(f"最大Token: {Config.MEMORY_CONFIG.get('max_token_size')}")
    print(f"自动刷新: {Config.MEMORY_CONFIG.get('auto_flush')}")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="初始化本地记忆数据库")
    parser.add_argument("--info", action="store_true", help="显示数据库配置信息")
    parser.add_argument("--force", action="store_true", help="强制重新初始化")
    
    args = parser.parse_args()
    
    if args.info:
        show_database_info()
    else:
        success = init_memory_database()
        sys.exit(0 if success else 1)
