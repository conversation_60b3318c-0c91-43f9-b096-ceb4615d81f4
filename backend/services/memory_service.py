"""
简化的记忆服务
直接操作PostgreSQL数据库，处理用户记忆的提取、存储和检索
"""

import logging
from typing import Dict, List
from datetime import datetime

from models.memory import get_memory_db
from services.embedding_service import get_embedding_service
from services.memory_analyzer import get_memory_analyzer
from config import Config

logger = logging.getLogger(__name__)


class MemoryService:
    """简化的记忆服务 - 直接操作数据库"""

    def __init__(self):
        self.db = get_memory_db()
        self.config = Config.MEMORY_CONFIG
        self.embedding_service = get_embedding_service()
        self.analyzer = get_memory_analyzer()
        logger.info("✅ 增强记忆服务初始化成功")
    
    def extract_memories_from_text(self, user_id: str, text: str) -> List[Dict]:
        """从文本中提取记忆信息（使用LLM和embedding）"""
        try:
            # 确保用户存在
            self.db.create_user(user_id)

            # 使用LLM分析文本
            analysis = self.analyzer.analyze_text(text, user_id)

            # 获取文本的embedding
            text_embedding = self.embedding_service.get_embedding(text)

            # 创建用户事件
            event_data = {
                'profile_delta': analysis.get('profile_deltas', []),
                'event_tip': analysis.get('event_tip', ''),
                'event_tags': analysis.get('event_tags', []),
                'original_text': text
            }

            event_id = self.db.add_user_event(
                user_id=user_id,
                event_data=event_data,
                embedding=text_embedding
            )

            # 添加提取的用户画像
            profile_ids = []
            for delta in analysis.get('profile_deltas', []):
                content = delta.get('content', '')
                attributes = delta.get('attributes', {})

                # 获取画像内容的embedding
                profile_embedding = self.embedding_service.get_embedding(content)

                profile_id = self.db.add_user_profile(
                    user_id=user_id,
                    content=content,
                    attributes=attributes,
                    embedding=profile_embedding
                )
                profile_ids.append(profile_id)

            logger.info(f"✅ 记忆提取完成 - 用户: {user_id}, 事件: {event_id}, 画像: {len(profile_ids)} 条")

            return [{
                'event_id': event_id,
                'profile_ids': profile_ids,
                'type': 'analyzed',
                'content': text[:100] + '...' if len(text) > 100 else text,
                'analysis': analysis,
                'timestamp': datetime.now().isoformat()
            }]

        except Exception as e:
            logger.error(f"💥 记忆提取失败: {e}")
            return []
    
    def get_relevant_memories(self, user_id: str, query_text: str = None,
                            limit: int = 5) -> List[Dict]:
        """获取相关记忆（使用向量搜索）"""
        try:
            memories = []

            if query_text:
                # 获取查询文本的embedding
                query_embedding = self.embedding_service.get_embedding(query_text)

                if query_embedding:
                    # 基于向量相似度搜索用户画像
                    similar_profiles = self.db.search_similar_profiles(
                        user_id=user_id,
                        query_embedding=query_embedding,
                        limit=limit,
                        threshold=0.6
                    )

                    for profile in similar_profiles:
                        attributes = profile.get('attributes', {})
                        memories.append({
                            'content': profile['content'],
                            'type': attributes.get('topic', 'unknown'),
                            'sub_type': attributes.get('sub_topic', ''),
                            'importance': attributes.get('confidence', 1.0),
                            'similarity': profile.get('similarity', 0.0),
                            'created_at': profile['created_at'].isoformat() if profile['created_at'] else '',
                            'source': 'profile_vector'
                        })

                    # 如果向量搜索结果不足，补充最新的画像
                    if len(memories) < limit:
                        recent_profiles = self.db.get_user_profiles(user_id, limit=limit - len(memories))
                        for profile in recent_profiles:
                            # 避免重复
                            if not any(m['content'] == profile['content'] for m in memories):
                                attributes = profile.get('attributes', {})
                                memories.append({
                                    'content': profile['content'],
                                    'type': attributes.get('topic', 'unknown'),
                                    'sub_type': attributes.get('sub_topic', ''),
                                    'importance': attributes.get('confidence', 1.0),
                                    'similarity': 0.5,  # 默认相似度
                                    'created_at': profile['created_at'].isoformat() if profile['created_at'] else '',
                                    'source': 'profile_recent'
                                })
            else:
                # 没有查询文本时，返回最新的画像
                profiles = self.db.get_user_profiles(user_id, limit=limit)
                for profile in profiles:
                    attributes = profile.get('attributes', {})
                    memories.append({
                        'content': profile['content'],
                        'type': attributes.get('topic', 'unknown'),
                        'sub_type': attributes.get('sub_topic', ''),
                        'importance': attributes.get('confidence', 1.0),
                        'similarity': 1.0,  # 最新记忆默认高相关性
                        'created_at': profile['created_at'].isoformat() if profile['created_at'] else '',
                        'source': 'profile_latest'
                    })

            logger.info(f"🔍 获取相关记忆 - 用户: {user_id}, 查询: {query_text[:30] if query_text else 'None'}, 结果: {len(memories)} 条")
            return memories[:limit]

        except Exception as e:
            logger.error(f"💥 获取相关记忆失败: {e}")
            return []
    
    def get_user_context(self, user_id: str, max_token_size: int = 500,
                        prefer_topics: List[str] = None, query_text: str = None) -> str:
        """获取用户上下文（支持向量搜索）"""
        try:
            prefer_topics = prefer_topics or ['basic_info', 'interests', 'personality']
            context_parts = []
            current_tokens = 0

            if query_text:
                # 使用向量搜索获取相关画像
                relevant_memories = self.get_relevant_memories(user_id, query_text, limit=10)

                # 按话题优先级排序
                sorted_memories = []
                for topic in prefer_topics:
                    topic_memories = [m for m in relevant_memories if m.get('type') == topic]
                    sorted_memories.extend(topic_memories)

                # 添加其他话题的记忆
                other_memories = [m for m in relevant_memories if m.get('type') not in prefer_topics]
                sorted_memories.extend(other_memories)

                # 构建上下文
                for memory in sorted_memories:
                    topic = memory.get('type', 'unknown')
                    sub_topic = memory.get('sub_topic', '')
                    content = memory.get('content', '')

                    if sub_topic:
                        formatted_content = f"[{topic}-{sub_topic}] {content}"
                    else:
                        formatted_content = f"[{topic}] {content}"

                    estimated_tokens = len(formatted_content) // 3

                    if current_tokens + estimated_tokens > max_token_size:
                        break

                    context_parts.append(formatted_content)
                    current_tokens += estimated_tokens
            else:
                # 没有查询文本时，按话题优先级获取
                for topic in prefer_topics:
                    profiles = self.db.get_user_profiles(user_id, topic=topic, limit=5)

                    for profile in profiles:
                        attributes = profile.get('attributes', {})
                        topic_name = attributes.get('topic', topic)
                        sub_topic = attributes.get('sub_topic', '')
                        content = profile.get('content', '')

                        if sub_topic:
                            formatted_content = f"[{topic_name}-{sub_topic}] {content}"
                        else:
                            formatted_content = f"[{topic_name}] {content}"

                        estimated_tokens = len(formatted_content) // 3

                        if current_tokens + estimated_tokens > max_token_size:
                            break

                        context_parts.append(formatted_content)
                        current_tokens += estimated_tokens

                    if current_tokens >= max_token_size * 0.8:
                        break

                # 如果还有空间，添加其他画像
                if current_tokens < max_token_size * 0.8:
                    other_profiles = self.db.get_user_profiles(user_id, limit=10)

                    for profile in other_profiles:
                        attributes = profile.get('attributes', {})
                        topic_name = attributes.get('topic', 'unknown')

                        if topic_name in prefer_topics:
                            continue  # 跳过已添加的话题

                        sub_topic = attributes.get('sub_topic', '')
                        content = profile.get('content', '')

                        if sub_topic:
                            formatted_content = f"[{topic_name}-{sub_topic}] {content}"
                        else:
                            formatted_content = f"[{topic_name}] {content}"

                        estimated_tokens = len(formatted_content) // 3

                        if current_tokens + estimated_tokens > max_token_size:
                            break

                        context_parts.append(formatted_content)
                        current_tokens += estimated_tokens

            context = "\n".join(context_parts)
            logger.info(f"📄 生成用户上下文: {len(context)} 字符, 预估 {current_tokens} tokens")

            return context

        except Exception as e:
            logger.error(f"💥 获取用户上下文失败: {e}")
            return ""
    


    def init_persona_memories(self, persona_id: str = None) -> bool:
        """初始化虚拟人记忆"""
        try:
            persona_id = persona_id or Config.PERSONA_CONFIG['persona_id_prefix'] + '_persona'

            # 检查是否已有记忆
            existing_profiles = self.db.get_user_profiles(persona_id, limit=1)
            if existing_profiles:
                logger.info(f"🤖 虚拟人 {persona_id} 已有记忆，跳过初始化")
                return True

            # 创建虚拟人用户
            self.db.create_user(persona_id, Config.PERSONA_CONFIG['name'])

            # 添加基础记忆（使用新的数据结构）
            basic_memories = [
                {
                    'content': f"我叫{Config.PERSONA_CONFIG['name']}，大家都叫我{Config.PERSONA_CONFIG['nickname']}",
                    'attributes': {'topic': 'basic_info', 'sub_topic': 'name', 'confidence': 1.0}
                },
                {
                    'content': f"我今年{Config.PERSONA_CONFIG['age']}岁，是{Config.PERSONA_CONFIG['profession']}",
                    'attributes': {'topic': 'basic_info', 'sub_topic': 'profession', 'confidence': 1.0}
                },
                {
                    'content': f"我住在{Config.PERSONA_CONFIG['location']}",
                    'attributes': {'topic': 'basic_info', 'sub_topic': 'location', 'confidence': 1.0}
                },
                {
                    'content': f"我的性格特点是{Config.PERSONA_CONFIG['personality']}",
                    'attributes': {'topic': 'personality', 'sub_topic': 'traits', 'confidence': 1.0}
                },
                {
                    'content': f"我说话的风格是{Config.PERSONA_CONFIG['speaking_style']}",
                    'attributes': {'topic': 'personality', 'sub_topic': 'speaking_style', 'confidence': 1.0}
                }
            ]

            # 添加爱好记忆
            for hobby in Config.PERSONA_CONFIG.get('hobbies', [])[:5]:
                basic_memories.append({
                    'content': hobby,
                    'attributes': {'topic': 'interests', 'sub_topic': 'hobbies', 'confidence': 1.0}
                })

            # 添加其他信息
            for info in Config.PERSONA_CONFIG.get('other_infos', [])[:5]:
                basic_memories.append({
                    'content': info,
                    'attributes': {'topic': 'basic_info', 'sub_topic': 'details', 'confidence': 1.0}
                })

            # 批量添加记忆（包含embedding）
            for memory in basic_memories:
                content = memory['content']
                attributes = memory['attributes']

                # 获取embedding
                embedding = self.embedding_service.get_embedding(content)

                self.db.add_user_profile(
                    user_id=persona_id,
                    content=content,
                    attributes=attributes,
                    embedding=embedding
                )

            logger.info(f"🤖 为虚拟人 {persona_id} 初始化了 {len(basic_memories)} 条记忆")
            return True

        except Exception as e:
            logger.error(f"💥 初始化虚拟人记忆失败: {e}")
            return False

    def get_persona_context(self, persona_id: str = None, max_token_size: int = 300) -> str:
        """获取虚拟人上下文"""
        try:
            persona_id = persona_id or Config.PERSONA_CONFIG['persona_id_prefix'] + '_persona'

            # 确保虚拟人记忆已初始化
            self.init_persona_memories(persona_id)

            # 获取虚拟人画像
            profiles = self.db.get_user_profiles(persona_id, limit=20)

            if not profiles:
                return ""

            # 按话题优先级排序
            prefer_topics = ['basic_info', 'personality', 'interests']
            context_parts = []
            current_tokens = 0

            # 先添加优先话题
            for topic in prefer_topics:
                topic_profiles = [p for p in profiles if p['topic'] == topic]

                for profile in topic_profiles:
                    content = f"[{profile['topic']}] {profile['content']}"
                    estimated_tokens = len(content) // 3

                    if current_tokens + estimated_tokens > max_token_size:
                        break

                    context_parts.append(content)
                    current_tokens += estimated_tokens

                if current_tokens >= max_token_size * 0.8:
                    break

            context = "\n".join(context_parts)
            logger.info(f"🤖 生成虚拟人上下文: {len(context)} 字符, 预估 {current_tokens} tokens")

            return context

        except Exception as e:
            logger.error(f"💥 获取虚拟人上下文失败: {e}")
            return ""

    def get_memory_stats(self) -> Dict:
        """获取记忆统计"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                
                # 统计各种数据
                cursor.execute('SELECT COUNT(*) FROM users')
                user_count = cursor.fetchone()[0]
                
                cursor.execute('SELECT COUNT(*) FROM memories')
                memory_count = cursor.fetchone()[0]
                
                cursor.execute('SELECT COUNT(*) FROM user_profiles')
                profile_count = cursor.fetchone()[0]
                
                cursor.execute('SELECT COUNT(*) FROM conversations')
                conversation_count = cursor.fetchone()[0]
                
                return {
                    'users': user_count,
                    'memories': memory_count,
                    'profiles': profile_count,
                    'conversations': conversation_count,
                    'provider': 'local_postgresql'
                }
                
        except Exception as e:
            logger.error(f"💥 获取统计失败: {e}")
            return {'error': str(e)}


# 全局服务实例
memory_service = None

def get_memory_service() -> MemoryService:
    """获取记忆服务实例"""
    global memory_service
    if memory_service is None:
        memory_service = MemoryService()
    return memory_service
