"""
简化的记忆服务
直接操作PostgreSQL数据库，处理用户记忆的提取、存储和检索
"""

import logging
import re
from typing import Dict, List, Optional
from datetime import datetime

from models.memory import get_memory_db
from config import Config

logger = logging.getLogger(__name__)


class MemoryService:
    """简化的记忆服务 - 直接操作数据库"""

    def __init__(self):
        self.db = get_memory_db()
        self.config = Config.MEMORY_CONFIG
        logger.info("✅ 简化记忆服务初始化成功")
    
    def extract_memories_from_text(self, user_id: str, text: str) -> List[Dict]:
        """从文本中提取记忆信息"""
        try:
            # 确保用户存在
            self.db.create_user(user_id)
            
            # 添加原始聊天记忆
            memory_id = self.db.add_memory(
                user_id=user_id,
                content=text,
                memory_type='chat',
                attributes={'source': 'user_input'},
                importance=1.0
            )
            
            # 简单的关键词提取和画像生成
            extracted_profiles = self._extract_simple_profiles(text)
            
            # 添加提取的画像
            for profile in extracted_profiles:
                self.db.add_user_profile(
                    user_id=user_id,
                    topic=profile['topic'],
                    content=profile['content'],
                    confidence=profile['confidence']
                )
            
            logger.info(f"✅ 记忆提取完成 - 用户: {user_id}, 提取画像: {len(extracted_profiles)} 条")
            
            return [{
                'id': memory_id,
                'type': 'chat',
                'content': text[:100] + '...' if len(text) > 100 else text,
                'importance': 1.0,
                'timestamp': datetime.now().isoformat(),
                'profiles_extracted': len(extracted_profiles)
            }]
            
        except Exception as e:
            logger.error(f"💥 记忆提取失败: {e}")
            return []
    
    def get_relevant_memories(self, user_id: str, query_text: str = None, 
                            limit: int = 5) -> List[Dict]:
        """获取相关记忆"""
        try:
            # 获取用户画像
            profiles = self.db.get_user_profiles(user_id, limit=limit)
            
            memories = []
            for profile in profiles:
                memories.append({
                    'content': profile['content'],
                    'type': profile['topic'],
                    'importance': profile['confidence'],
                    'created_at': profile['created_at'].isoformat() if profile['created_at'] else '',
                    'source': 'profile'
                })
            
            # 如果画像不足，补充聊天记忆
            if len(memories) < limit:
                chat_memories = self.db.get_memories(
                    user_id, 
                    memory_type='chat', 
                    limit=limit - len(memories)
                )
                
                for memory in chat_memories:
                    memories.append({
                        'content': memory['content'],
                        'type': memory['memory_type'],
                        'importance': memory['importance'],
                        'created_at': memory['created_at'].isoformat() if memory['created_at'] else '',
                        'source': 'memory'
                    })
            
            logger.info(f"🔍 获取记忆 - 用户: {user_id}, 结果: {len(memories)} 条")
            return memories
            
        except Exception as e:
            logger.error(f"💥 获取记忆失败: {e}")
            return []
    
    def get_user_context(self, user_id: str, max_token_size: int = 500, 
                        prefer_topics: List[str] = None) -> str:
        """获取用户上下文"""
        try:
            prefer_topics = prefer_topics or ['basic_info', 'interests', 'personality']
            
            # 按优先话题获取画像
            context_parts = []
            current_tokens = 0
            
            for topic in prefer_topics:
                profiles = self.db.get_user_profiles(user_id, topic=topic, limit=5)
                
                for profile in profiles:
                    content = f"[{profile['topic']}] {profile['content']}"
                    estimated_tokens = len(content) // 3  # 粗略估算
                    
                    if current_tokens + estimated_tokens > max_token_size:
                        break
                    
                    context_parts.append(content)
                    current_tokens += estimated_tokens
                
                if current_tokens >= max_token_size * 0.8:  # 达到80%就停止
                    break
            
            # 如果还有空间，添加其他画像
            if current_tokens < max_token_size * 0.8:
                other_profiles = self.db.get_user_profiles(user_id, limit=10)
                
                for profile in other_profiles:
                    if profile['topic'] in prefer_topics:
                        continue  # 跳过已添加的话题
                    
                    content = f"[{profile['topic']}] {profile['content']}"
                    estimated_tokens = len(content) // 3
                    
                    if current_tokens + estimated_tokens > max_token_size:
                        break
                    
                    context_parts.append(content)
                    current_tokens += estimated_tokens
            
            context = "\n".join(context_parts)
            logger.info(f"📄 生成用户上下文: {len(context)} 字符, 预估 {current_tokens} tokens")
            
            return context
            
        except Exception as e:
            logger.error(f"💥 获取用户上下文失败: {e}")
            return ""
    
    def _extract_simple_profiles(self, text: str) -> List[Dict]:
        """简单的画像提取"""
        profiles = []
        
        # 关键词模式匹配
        patterns = {
            'basic_info': [
                (r'我是(.{1,20})', '身份'),
                (r'我叫(.{1,10})', '姓名'),
                (r'我今年(.{1,10})岁', '年龄'),
                (r'我在(.{1,20})工作', '工作地点'),
                (r'我的工作是(.{1,20})', '职业'),
            ],
            'interests': [
                (r'我喜欢(.{1,30})', '喜好'),
                (r'我爱(.{1,20})', '爱好'),
                (r'我的兴趣是(.{1,30})', '兴趣'),
                (r'我经常(.{1,30})', '习惯'),
            ],
            'personality': [
                (r'我是一个(.{1,20})的人', '性格特点'),
                (r'我比较(.{1,15})', '性格倾向'),
                (r'我觉得自己(.{1,20})', '自我认知'),
            ],
            'experience': [
                (r'我去过(.{1,30})', '旅行经历'),
                (r'我学过(.{1,20})', '学习经历'),
                (r'我做过(.{1,30})', '工作经历'),
            ],
            'emotion': [
                (r'我感到(.{1,15})', '情感状态'),
                (r'我很(.{1,10})', '情绪'),
                (r'让我(.{1,15})', '情感反应'),
            ]
        }
        
        for topic, topic_patterns in patterns.items():
            for pattern, sub_topic in topic_patterns:
                matches = re.findall(pattern, text)
                for match in matches:
                    if len(match.strip()) > 1:  # 过滤太短的内容
                        profiles.append({
                            'topic': topic,
                            'content': f"{sub_topic}: {match.strip()}",
                            'confidence': 0.7  # 简单提取的置信度
                        })
        
        return profiles[:5]  # 限制数量

    def init_persona_memories(self, persona_id: str = None) -> bool:
        """初始化虚拟人记忆"""
        try:
            persona_id = persona_id or Config.PERSONA_CONFIG['persona_id_prefix'] + '_persona'

            # 检查是否已有记忆
            existing_profiles = self.db.get_user_profiles(persona_id, limit=1)
            if existing_profiles:
                logger.info(f"🤖 虚拟人 {persona_id} 已有记忆，跳过初始化")
                return True

            # 创建虚拟人用户
            self.db.create_user(persona_id, Config.PERSONA_CONFIG['name'])

            # 添加基础记忆
            basic_memories = [
                ('basic_info', f"我叫{Config.PERSONA_CONFIG['name']}，大家都叫我{Config.PERSONA_CONFIG['nickname']}"),
                ('basic_info', f"我今年{Config.PERSONA_CONFIG['age']}岁，是{Config.PERSONA_CONFIG['profession']}"),
                ('basic_info', f"我住在{Config.PERSONA_CONFIG['location']}"),
                ('personality', f"我的性格特点是{Config.PERSONA_CONFIG['personality']}"),
                ('personality', f"我说话的风格是{Config.PERSONA_CONFIG['speaking_style']}"),
            ]

            # 添加爱好记忆
            for hobby in Config.PERSONA_CONFIG.get('hobbies', [])[:5]:
                basic_memories.append(('interests', hobby))

            # 添加其他信息
            for info in Config.PERSONA_CONFIG.get('other_infos', [])[:5]:
                basic_memories.append(('basic_info', info))

            # 批量添加记忆
            for topic, content in basic_memories:
                self.db.add_user_profile(persona_id, topic, content, confidence=1.0)

            logger.info(f"🤖 为虚拟人 {persona_id} 初始化了 {len(basic_memories)} 条记忆")
            return True

        except Exception as e:
            logger.error(f"💥 初始化虚拟人记忆失败: {e}")
            return False

    def get_persona_context(self, persona_id: str = None, max_token_size: int = 300) -> str:
        """获取虚拟人上下文"""
        try:
            persona_id = persona_id or Config.PERSONA_CONFIG['persona_id_prefix'] + '_persona'

            # 确保虚拟人记忆已初始化
            self.init_persona_memories(persona_id)

            # 获取虚拟人画像
            profiles = self.db.get_user_profiles(persona_id, limit=20)

            if not profiles:
                return ""

            # 按话题优先级排序
            prefer_topics = ['basic_info', 'personality', 'interests']
            context_parts = []
            current_tokens = 0

            # 先添加优先话题
            for topic in prefer_topics:
                topic_profiles = [p for p in profiles if p['topic'] == topic]

                for profile in topic_profiles:
                    content = f"[{profile['topic']}] {profile['content']}"
                    estimated_tokens = len(content) // 3

                    if current_tokens + estimated_tokens > max_token_size:
                        break

                    context_parts.append(content)
                    current_tokens += estimated_tokens

                if current_tokens >= max_token_size * 0.8:
                    break

            context = "\n".join(context_parts)
            logger.info(f"🤖 生成虚拟人上下文: {len(context)} 字符, 预估 {current_tokens} tokens")

            return context

        except Exception as e:
            logger.error(f"💥 获取虚拟人上下文失败: {e}")
            return ""

    def get_memory_stats(self) -> Dict:
        """获取记忆统计"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                
                # 统计各种数据
                cursor.execute('SELECT COUNT(*) FROM users')
                user_count = cursor.fetchone()[0]
                
                cursor.execute('SELECT COUNT(*) FROM memories')
                memory_count = cursor.fetchone()[0]
                
                cursor.execute('SELECT COUNT(*) FROM user_profiles')
                profile_count = cursor.fetchone()[0]
                
                cursor.execute('SELECT COUNT(*) FROM conversations')
                conversation_count = cursor.fetchone()[0]
                
                return {
                    'users': user_count,
                    'memories': memory_count,
                    'profiles': profile_count,
                    'conversations': conversation_count,
                    'provider': 'local_postgresql'
                }
                
        except Exception as e:
            logger.error(f"💥 获取统计失败: {e}")
            return {'error': str(e)}


# 全局服务实例
memory_service = None

def get_memory_service() -> MemoryService:
    """获取记忆服务实例"""
    global memory_service
    if memory_service is None:
        memory_service = MemoryService()
    return memory_service
