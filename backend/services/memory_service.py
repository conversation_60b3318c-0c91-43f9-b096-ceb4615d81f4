"""
简化的记忆服务
处理用户记忆的提取、存储和检索
"""

import logging
import re
from typing import Dict, List, Optional
from datetime import datetime

from models.memory import get_memory_db
from config import Config

logger = logging.getLogger(__name__)


class MemoryService:
    """记忆服务"""
    
    def __init__(self):
        self.db = get_memory_db()
        self.config = Config.MEMORY_CONFIG
        logger.info("✅ 记忆服务初始化成功")
    
    def extract_memories_from_text(self, user_id: str, text: str) -> List[Dict]:
        """从文本中提取记忆信息"""
        try:
            # 确保用户存在
            self.db.create_user(user_id)
            
            # 添加原始聊天记忆
            memory_id = self.db.add_memory(
                user_id=user_id,
                content=text,
                memory_type='chat',
                attributes={'source': 'user_input'},
                importance=1.0
            )
            
            # 简单的关键词提取和画像生成
            extracted_profiles = self._extract_simple_profiles(text)
            
            # 添加提取的画像
            for profile in extracted_profiles:
                self.db.add_user_profile(
                    user_id=user_id,
                    topic=profile['topic'],
                    content=profile['content'],
                    confidence=profile['confidence']
                )
            
            logger.info(f"✅ 记忆提取完成 - 用户: {user_id}, 提取画像: {len(extracted_profiles)} 条")
            
            return [{
                'id': memory_id,
                'type': 'chat',
                'content': text[:100] + '...' if len(text) > 100 else text,
                'importance': 1.0,
                'timestamp': datetime.now().isoformat(),
                'profiles_extracted': len(extracted_profiles)
            }]
            
        except Exception as e:
            logger.error(f"💥 记忆提取失败: {e}")
            return []
    
    def get_relevant_memories(self, user_id: str, query_text: str = None, 
                            limit: int = 5) -> List[Dict]:
        """获取相关记忆"""
        try:
            # 获取用户画像
            profiles = self.db.get_user_profiles(user_id, limit=limit)
            
            memories = []
            for profile in profiles:
                memories.append({
                    'content': profile['content'],
                    'type': profile['topic'],
                    'importance': profile['confidence'],
                    'created_at': profile['created_at'].isoformat() if profile['created_at'] else '',
                    'source': 'profile'
                })
            
            # 如果画像不足，补充聊天记忆
            if len(memories) < limit:
                chat_memories = self.db.get_memories(
                    user_id, 
                    memory_type='chat', 
                    limit=limit - len(memories)
                )
                
                for memory in chat_memories:
                    memories.append({
                        'content': memory['content'],
                        'type': memory['memory_type'],
                        'importance': memory['importance'],
                        'created_at': memory['created_at'].isoformat() if memory['created_at'] else '',
                        'source': 'memory'
                    })
            
            logger.info(f"🔍 获取记忆 - 用户: {user_id}, 结果: {len(memories)} 条")
            return memories
            
        except Exception as e:
            logger.error(f"💥 获取记忆失败: {e}")
            return []
    
    def get_user_context(self, user_id: str, max_token_size: int = 500, 
                        prefer_topics: List[str] = None) -> str:
        """获取用户上下文"""
        try:
            prefer_topics = prefer_topics or ['basic_info', 'interests', 'personality']
            
            # 按优先话题获取画像
            context_parts = []
            current_tokens = 0
            
            for topic in prefer_topics:
                profiles = self.db.get_user_profiles(user_id, topic=topic, limit=5)
                
                for profile in profiles:
                    content = f"[{profile['topic']}] {profile['content']}"
                    estimated_tokens = len(content) // 3  # 粗略估算
                    
                    if current_tokens + estimated_tokens > max_token_size:
                        break
                    
                    context_parts.append(content)
                    current_tokens += estimated_tokens
                
                if current_tokens >= max_token_size * 0.8:  # 达到80%就停止
                    break
            
            # 如果还有空间，添加其他画像
            if current_tokens < max_token_size * 0.8:
                other_profiles = self.db.get_user_profiles(user_id, limit=10)
                
                for profile in other_profiles:
                    if profile['topic'] in prefer_topics:
                        continue  # 跳过已添加的话题
                    
                    content = f"[{profile['topic']}] {profile['content']}"
                    estimated_tokens = len(content) // 3
                    
                    if current_tokens + estimated_tokens > max_token_size:
                        break
                    
                    context_parts.append(content)
                    current_tokens += estimated_tokens
            
            context = "\n".join(context_parts)
            logger.info(f"📄 生成用户上下文: {len(context)} 字符, 预估 {current_tokens} tokens")
            
            return context
            
        except Exception as e:
            logger.error(f"💥 获取用户上下文失败: {e}")
            return ""
    
    def _extract_simple_profiles(self, text: str) -> List[Dict]:
        """简单的画像提取"""
        profiles = []
        
        # 关键词模式匹配
        patterns = {
            'basic_info': [
                (r'我是(.{1,20})', '身份'),
                (r'我叫(.{1,10})', '姓名'),
                (r'我今年(.{1,10})岁', '年龄'),
                (r'我在(.{1,20})工作', '工作地点'),
                (r'我的工作是(.{1,20})', '职业'),
            ],
            'interests': [
                (r'我喜欢(.{1,30})', '喜好'),
                (r'我爱(.{1,20})', '爱好'),
                (r'我的兴趣是(.{1,30})', '兴趣'),
                (r'我经常(.{1,30})', '习惯'),
            ],
            'personality': [
                (r'我是一个(.{1,20})的人', '性格特点'),
                (r'我比较(.{1,15})', '性格倾向'),
                (r'我觉得自己(.{1,20})', '自我认知'),
            ],
            'experience': [
                (r'我去过(.{1,30})', '旅行经历'),
                (r'我学过(.{1,20})', '学习经历'),
                (r'我做过(.{1,30})', '工作经历'),
            ],
            'emotion': [
                (r'我感到(.{1,15})', '情感状态'),
                (r'我很(.{1,10})', '情绪'),
                (r'让我(.{1,15})', '情感反应'),
            ]
        }
        
        for topic, topic_patterns in patterns.items():
            for pattern, sub_topic in topic_patterns:
                matches = re.findall(pattern, text)
                for match in matches:
                    if len(match.strip()) > 1:  # 过滤太短的内容
                        profiles.append({
                            'topic': topic,
                            'content': f"{sub_topic}: {match.strip()}",
                            'confidence': 0.7  # 简单提取的置信度
                        })
        
        return profiles[:5]  # 限制数量
    
    def get_memory_stats(self) -> Dict:
        """获取记忆统计"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                
                # 统计各种数据
                cursor.execute('SELECT COUNT(*) FROM users')
                user_count = cursor.fetchone()[0]
                
                cursor.execute('SELECT COUNT(*) FROM memories')
                memory_count = cursor.fetchone()[0]
                
                cursor.execute('SELECT COUNT(*) FROM user_profiles')
                profile_count = cursor.fetchone()[0]
                
                cursor.execute('SELECT COUNT(*) FROM conversations')
                conversation_count = cursor.fetchone()[0]
                
                return {
                    'users': user_count,
                    'memories': memory_count,
                    'profiles': profile_count,
                    'conversations': conversation_count,
                    'provider': 'local_postgresql'
                }
                
        except Exception as e:
            logger.error(f"💥 获取统计失败: {e}")
            return {'error': str(e)}


# 全局服务实例
memory_service = None

def get_memory_service() -> MemoryService:
    """获取记忆服务实例"""
    global memory_service
    if memory_service is None:
        memory_service = MemoryService()
    return memory_service
