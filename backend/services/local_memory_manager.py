"""
本地记忆管理器
替换原有的Memobase记忆管理器，使用本地PostgreSQL数据库
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

from clients.local_memory_client import LocalMemoryClient, ChatBlob
from config import Config

# 配置日志
logger = logging.getLogger(__name__)


class LocalMemoryManager:
    """本地记忆管理器"""

    def __init__(self, llm_service=None):
        """
        初始化本地记忆管理器

        Args:
            llm_service: LLM服务实例（保留用于兼容性）
        """
        self.llm_service = llm_service
        self.config = Config.MEMORY_CONFIG
        
        # 初始化本地记忆客户端
        try:
            self.memory_client = LocalMemoryClient(
                database_url=self.config.get('database_url')
            )
            logger.info("✅ 本地记忆管理器初始化成功")
        except Exception as e:
            logger.error(f"❌ 本地记忆管理器初始化失败: {e}")
            raise

        # 记忆类型定义（保持与原系统兼容）
        self.memory_types = {
            'personal': '个人信息',
            'preference': '喜好偏好',
            'experience': '经历体验',
            'goal': '目标计划',
            'relationship': '关系状态',
            'emotion': '情感状态'
        }

        # 重要性等级
        self.importance_levels = {
            'low': 1.0,
            'medium': 2.0,
            'high': 3.0
        }

    def extract_memories_from_text(self, user_id: str, text: str) -> List[Dict]:
        """
        从文本中提取记忆信息

        Args:
            user_id: 用户ID
            text: 输入文本

        Returns:
            提取的记忆信息列表
        """
        request_id = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        logger.info(f"🧠 开始本地记忆提取 [{request_id}] - 用户: {user_id}")
        logger.info(f"📝 提取文本: {text}")

        try:
            # 获取或创建用户
            user = self.memory_client.get_or_create_user(user_id)
            
            # 构造聊天消息格式
            messages = [
                {
                    "role": "user",
                    "content": text,
                    "timestamp": datetime.now().isoformat()
                }
            ]
            
            # 插入聊天数据到本地数据库
            blob_id = self.memory_client.insert_chat_data(user, messages)
            
            # 如果配置了自动刷新，立即刷新记忆
            if self.config.get('auto_flush', True):
                self.memory_client.flush_user_memory(user)
            
            logger.info(f"✅ 记忆提取完成 [{request_id}] - Blob ID: {blob_id}")
            
            # 返回模拟的记忆信息（实际记忆由本地服务管理）
            return [{
                'type': 'auto_extracted',
                'content': f"已通过本地记忆处理: {text[:50]}...",
                'importance': 1.0,
                'timestamp': datetime.now().isoformat()
            }]
            
        except Exception as e:
            logger.error(f"💥 本地记忆提取失败 [{request_id}]: {str(e)}")
            return []

    def get_relevant_memories(self, user_id: str, query_text: str = None, keywords: List[str] = None,
                            limit: int = None, similarity_threshold: float = None) -> List[Dict]:
        """
        获取相关记忆（基于本地数据库的用户画像）

        Args:
            user_id: 用户ID
            query_text: 查询文本（用于上下文生成）
            keywords: 关键词列表（备用）
            limit: 返回数量限制
            similarity_threshold: 相似度阈值（保留用于兼容性）

        Returns:
            相关记忆列表
        """
        try:
            # 获取用户
            user = self.memory_client.get_or_create_user(user_id)
            logger.debug(f"🔍 获取用户对象成功: {user_id}")

            memories = []

            # 获取用户画像
            try:
                profile = self.memory_client.get_user_profile(user, need_json=True)
                logger.debug(f"📋 用户画像类型: {type(profile)}, 长度: {len(profile) if hasattr(profile, '__len__') else 'N/A'}")

                if profile and isinstance(profile, list):
                    for item in profile:
                        if isinstance(item, dict) and 'content' in item:
                            attributes = item.get('attributes', {})
                            memories.append({
                                'content': item['content'],
                                'type': attributes.get('topic', 'general'),
                                'sub_type': attributes.get('sub_topic', ''),
                                'importance': attributes.get('confidence', 1.0) * 2.0,  # 转换置信度为重要性
                                'created_at': item.get('created_at', datetime.now().isoformat()),
                                'similarity': 1.0,  # 本地处理的相关性
                                'recall_count': 1,
                                'source': 'profile'
                            })
                            logger.debug(f"✅ 添加画像记忆: {item['content'][:50]}...")

            except Exception as profile_error:
                logger.warning(f"⚠️ 获取用户画像失败: {profile_error}")

            # 应用限制
            if limit and len(memories) > limit:
                memories = memories[:limit]

            logger.info(f"🔍 本地记忆获取 - 用户: {user_id}, 结果: {len(memories)} 条")

            # 如果仍然没有记忆，记录详细信息
            if not memories:
                logger.warning(f"⚠️ 用户 {user_id} 没有找到任何记忆，可能需要更多聊天数据来生成画像")

            return memories

        except Exception as e:
            logger.error(f"💥 获取本地记忆失败 - 用户: {user_id}, 错误: {str(e)}")
            import traceback
            logger.error(f"💥 详细错误信息: {traceback.format_exc()}")
            return []

    def flush_user_memory(self, user_id: str) -> bool:
        """
        手动刷新用户记忆

        Args:
            user_id: 用户ID

        Returns:
            是否成功
        """
        try:
            user = self.memory_client.get_or_create_user(user_id)
            return self.memory_client.flush_user_memory(user)
        except Exception as e:
            logger.error(f"💥 刷新用户记忆失败 - 用户: {user_id}, 错误: {str(e)}")
            return False

    def get_memory_statistics(self) -> Dict:
        """
        获取记忆统计信息

        Returns:
            记忆统计信息
        """
        try:
            stats = self.memory_client.get_usage_stats()
            return {
                'total_memories': stats.get('profiles', 0),
                'total_users': stats.get('users', 0),
                'total_blobs': stats.get('blobs', 0),
                'by_user': {},  # 可以后续扩展
                'by_type': {},  # 可以后续扩展
                'avg_importance': 1.5,  # 默认值
                'provider': 'local_memory'
            }
        except Exception as e:
            logger.error(f"💥 获取记忆统计失败: {e}")
            return {
                'total_memories': 0,
                'total_users': 0,
                'total_blobs': 0,
                'by_user': {},
                'by_type': {},
                'avg_importance': 0.0,
                'provider': 'local_memory',
                'error': str(e)
            }

    def get_user_context(self, user_id: str, max_token_size: int = None, 
                        prefer_topics: List[str] = None, chats: List[Dict] = None,
                        optimize_tokens: bool = False) -> str:
        """
        获取用户上下文

        Args:
            user_id: 用户ID
            max_token_size: 最大token数量
            prefer_topics: 优先话题
            chats: 聊天历史
            optimize_tokens: 是否优化token使用

        Returns:
            用户上下文字符串
        """
        try:
            user = self.memory_client.get_or_create_user(user_id)
            
            max_token_size = max_token_size or self.config.get('max_token_size', 500)
            prefer_topics = prefer_topics or self.config.get('prefer_topics', [])
            
            return self.memory_client.get_user_context(
                user, max_token_size, prefer_topics, chats, optimize_tokens
            )
        except Exception as e:
            logger.error(f"💥 获取用户上下文失败: {e}")
            return ""

    def ping(self) -> bool:
        """测试连接"""
        return self.memory_client.ping()


# 兼容性别名
MemobaseMemoryManager = LocalMemoryManager
