"""
本地记忆服务
替换memobase的核心功能，直接使用PostgreSQL存储
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import json
import re

from models.memory_models import (
    DatabaseManager, BlobType, OpenAICompatibleMessage, 
    EventData, ProfileDelta, EventTag
)
from config import Config

logger = logging.getLogger(__name__)


class LocalMemoryService:
    """本地记忆服务"""
    
    def __init__(self, database_url: str = None):
        """初始化本地记忆服务"""
        self.db_manager = DatabaseManager(database_url)
        self.config = Config.MEMOBASE_CONFIG
        self.project_id = self.config.get('project_id', 'default')
        
        logger.info("✅ 本地记忆服务初始化成功")
    
    def insert_chat_data(self, user_id: str, messages: List[Dict]) -> str:
        """插入聊天数据"""
        try:
            # 验证消息格式
            validated_messages = []
            for msg in messages:
                validated_msg = OpenAICompatibleMessage(**msg)
                validated_messages.append(validated_msg.dict())
            
            # 插入到数据库
            blob_id = self.db_manager.insert_blob(
                user_id=user_id,
                blob_type=BlobType.CHAT,
                blob_data={"messages": validated_messages},
                project_id=self.project_id
            )
            
            logger.info(f"💬 插入聊天数据成功: 用户={user_id}, blob_id={blob_id}")
            return blob_id
            
        except Exception as e:
            logger.error(f"💥 插入聊天数据失败: {e}")
            raise
    
    def get_user_profile(self, user_id: str, need_json: bool = True) -> List[Dict] | str:
        """获取用户画像"""
        try:
            profiles = self.db_manager.get_user_profiles(user_id, self.project_id)
            
            if not profiles:
                logger.info(f"📋 用户 {user_id} 暂无画像数据")
                return [] if need_json else ""
            
            if need_json:
                return [
                    {
                        "id": str(profile.id),
                        "content": profile.content,
                        "attributes": profile.attributes or {},
                        "created_at": profile.created_at.isoformat(),
                        "updated_at": profile.updated_at.isoformat()
                    }
                    for profile in profiles
                ]
            else:
                # 返回格式化的文本
                profile_texts = []
                for profile in profiles:
                    attr = profile.attributes or {}
                    topic = attr.get('topic', '未分类')
                    sub_topic = attr.get('sub_topic', '')
                    
                    if sub_topic:
                        profile_texts.append(f"[{topic}-{sub_topic}] {profile.content}")
                    else:
                        profile_texts.append(f"[{topic}] {profile.content}")
                
                return "\n".join(profile_texts)
                
        except Exception as e:
            logger.error(f"💥 获取用户画像失败: {e}")
            return [] if need_json else ""
    
    def get_user_context(self, user_id: str, max_token_size: int = 500, 
                        prefer_topics: List[str] = None, chats: List[Dict] = None,
                        optimize_tokens: bool = False) -> str:
        """获取用户上下文"""
        try:
            # 获取用户画像
            profiles = self.db_manager.get_user_profiles(user_id, self.project_id)
            
            if not profiles:
                return ""
            
            # 按优先话题排序
            if prefer_topics:
                priority_profiles = []
                other_profiles = []
                
                for profile in profiles:
                    attr = profile.attributes or {}
                    topic = attr.get('topic', '')
                    if topic in prefer_topics:
                        priority_profiles.append(profile)
                    else:
                        other_profiles.append(profile)
                
                # 按优先级和更新时间排序
                priority_profiles.sort(key=lambda p: (
                    prefer_topics.index(p.attributes.get('topic', '')) if p.attributes.get('topic') in prefer_topics else 999,
                    p.updated_at
                ), reverse=True)
                
                other_profiles.sort(key=lambda p: p.updated_at, reverse=True)
                profiles = priority_profiles + other_profiles
            else:
                profiles.sort(key=lambda p: p.updated_at, reverse=True)
            
            # 构建上下文，控制token数量
            context_parts = []
            current_tokens = 0
            
            for profile in profiles:
                attr = profile.attributes or {}
                topic = attr.get('topic', '未分类')
                sub_topic = attr.get('sub_topic', '')
                
                if sub_topic:
                    profile_text = f"[{topic}-{sub_topic}] {profile.content}"
                else:
                    profile_text = f"[{topic}] {profile.content}"
                
                # 估算token数量
                estimated_tokens = len(profile_text) // 3
                
                if current_tokens + estimated_tokens > max_token_size:
                    if optimize_tokens and current_tokens < max_token_size * 0.8:
                        # 截断当前画像以适应token限制
                        remaining_chars = (max_token_size - current_tokens) * 3
                        if remaining_chars > 50:  # 至少保留50个字符
                            truncated_content = profile.content[:remaining_chars-20] + "..."
                            if sub_topic:
                                profile_text = f"[{topic}-{sub_topic}] {truncated_content}"
                            else:
                                profile_text = f"[{topic}] {truncated_content}"
                            context_parts.append(profile_text)
                    break
                
                context_parts.append(profile_text)
                current_tokens += estimated_tokens
            
            context = "\n".join(context_parts)
            
            # 记录token使用情况
            char_count = len(context)
            estimated_tokens = char_count // 3
            logger.info(f"📄 生成用户上下文: {char_count} 字符, 预估 {estimated_tokens} tokens")
            
            return context
            
        except Exception as e:
            logger.error(f"💥 获取用户上下文失败: {e}")
            return ""
    
    def flush_user_memory(self, user_id: str) -> bool:
        """刷新用户记忆（处理未处理的blob）"""
        try:
            # 获取未处理的聊天数据
            blobs = self.db_manager.get_user_blobs(
                user_id=user_id, 
                blob_type=BlobType.CHAT, 
                project_id=self.project_id,
                limit=50
            )
            
            unprocessed_blobs = [blob for blob in blobs if not blob.processed]
            
            if not unprocessed_blobs:
                logger.info(f"🔄 用户 {user_id} 没有需要处理的记忆数据")
                return True
            
            # 提取聊天内容进行画像分析
            all_messages = []
            for blob in unprocessed_blobs:
                messages = blob.blob_data.get('messages', [])
                all_messages.extend(messages)
            
            if all_messages:
                # 简化的画像提取（实际项目中可以集成LLM进行更智能的提取）
                extracted_profiles = self._extract_simple_profiles(all_messages)
                
                if extracted_profiles:
                    # 添加到用户画像
                    profiles = [p['content'] for p in extracted_profiles]
                    attributes = [p['attributes'] for p in extracted_profiles]
                    
                    self.db_manager.add_user_profiles(
                        user_id=user_id,
                        profiles=profiles,
                        attributes=attributes,
                        project_id=self.project_id
                    )
                    
                    logger.info(f"🧠 为用户 {user_id} 提取了 {len(extracted_profiles)} 条画像")
            
            # 标记blob为已处理
            with self.db_manager.get_session() as session:
                for blob in unprocessed_blobs:
                    blob.processed = True
                session.commit()
            
            logger.info(f"🔄 用户 {user_id} 记忆刷新完成")
            return True
            
        except Exception as e:
            logger.error(f"💥 刷新用户记忆失败: {e}")
            return False
    
    def _extract_simple_profiles(self, messages: List[Dict]) -> List[Dict]:
        """简化的画像提取（可以后续集成LLM优化）"""
        profiles = []
        
        # 合并所有用户消息
        user_texts = []
        for msg in messages:
            if msg.get('role') == 'user':
                user_texts.append(msg.get('content', ''))
        
        if not user_texts:
            return profiles
        
        combined_text = " ".join(user_texts)
        
        # 简单的关键词提取
        keywords_patterns = {
            'basic_info': [r'我是', r'我叫', r'我的名字', r'我今年', r'岁'],
            'interests_hobbies': [r'喜欢', r'爱好', r'兴趣', r'喜爱'],
            'emotional_state': [r'开心', r'难过', r'高兴', r'郁闷', r'兴奋'],
            'work_study': [r'工作', r'学习', r'上班', r'学校', r'公司'],
        }
        
        for topic, patterns in keywords_patterns.items():
            for pattern in patterns:
                matches = re.findall(f'.{{0,20}}{pattern}.{{0,30}}', combined_text)
                for match in matches:
                    if len(match.strip()) > 5:  # 过滤太短的内容
                        profiles.append({
                            'content': match.strip(),
                            'attributes': {
                                'topic': topic,
                                'sub_topic': 'auto_extracted',
                                'confidence': 0.6
                            }
                        })
        
        return profiles[:10]  # 限制数量
    
    def ping(self) -> bool:
        """测试连接"""
        try:
            # 简单的数据库连接测试
            with self.db_manager.get_session() as session:
                session.execute("SELECT 1")
            return True
        except Exception as e:
            logger.error(f"💥 连接测试失败: {e}")
            return False
