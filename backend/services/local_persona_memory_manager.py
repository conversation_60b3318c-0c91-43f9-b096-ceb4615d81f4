"""
本地虚拟人记忆管理器
管理虚拟人的个人记忆、背景故事和经历
基于本地PostgreSQL数据库
"""

import logging
from datetime import datetime
from typing import List, Dict, Optional, Any

from clients.local_memory_client import LocalMemoryClient, ChatBlob
from config import Config
from services.persona_id_manager import get_persona_id_manager

# 配置日志
logger = logging.getLogger(__name__)


class LocalPersonaMemoryManager:
    """本地虚拟人记忆管理器"""

    def __init__(self):
        """初始化虚拟人记忆管理器"""
        self.config = Config.MEMORY_CONFIG
        self.persona_config = Config.PERSONA_CONFIG
        
        # 初始化本地记忆客户端
        try:
            self.memory_client = LocalMemoryClient(
                database_url=self.config.get('database_url')
            )
            logger.info("✅ 本地虚拟人记忆管理器初始化成功")
        except Exception as e:
            logger.error(f"❌ 本地虚拟人记忆管理器初始化失败: {e}")
            raise

        # 获取虚拟人唯一ID
        try:
            persona_id_manager = get_persona_id_manager()
            self.persona_user_id = persona_id_manager.get_persona_id()
            logger.info(f"🤖 虚拟人ID: {self.persona_user_id}")
        except Exception as e:
            logger.error(f"❌ 获取虚拟人ID失败: {e}")
            self.persona_user_id = "persona_shenmuxi_default"

        # 初始化虚拟人基础记忆
        self._initialize_persona_memories()

    def _initialize_persona_memories(self):
        """初始化虚拟人的基础记忆"""
        try:
            # 获取虚拟人用户
            persona_user = self.memory_client.get_or_create_user(self.persona_user_id)
            
            # 检查是否已有基础记忆
            existing_profiles = self.memory_client.get_user_profile(persona_user, need_json=True)
            if existing_profiles:
                logger.info(f"🧠 虚拟人已有 {len(existing_profiles)} 条记忆")
                return

            # 添加基础个人信息记忆
            basic_memories = [
                {
                    'content': f"我叫{self.persona_config['name']}，大家都叫我{self.persona_config['nickname']}",
                    'attributes': {'topic': 'basic_info', 'sub_topic': 'name', 'importance': 'high'}
                },
                {
                    'content': f"我今年{self.persona_config['age']}岁，是{self.persona_config['profession']}",
                    'attributes': {'topic': 'basic_info', 'sub_topic': 'profession', 'importance': 'high'}
                },
                {
                    'content': f"我住在{self.persona_config['location']}",
                    'attributes': {'topic': 'basic_info', 'sub_topic': 'location', 'importance': 'medium'}
                },
                {
                    'content': f"我的性格特点是{self.persona_config['personality']}",
                    'attributes': {'topic': 'personality', 'sub_topic': 'traits', 'importance': 'high'}
                },
                {
                    'content': f"我说话的风格是{self.persona_config['speaking_style']}",
                    'attributes': {'topic': 'personality', 'sub_topic': 'speaking_style', 'importance': 'high'}
                }
            ]

            # 添加爱好记忆
            for hobby in self.persona_config.get('hobbies', [])[:10]:  # 限制数量
                basic_memories.append({
                    'content': hobby,
                    'attributes': {'topic': 'interests_hobbies', 'sub_topic': 'hobbies', 'importance': 'medium'}
                })

            # 添加其他信息记忆
            for info in self.persona_config.get('other_infos', [])[:10]:  # 限制数量
                basic_memories.append({
                    'content': info,
                    'attributes': {'topic': 'basic_info', 'sub_topic': 'details', 'importance': 'medium'}
                })

            # 批量添加记忆
            if basic_memories:
                profiles = [mem['content'] for mem in basic_memories]
                attributes = [mem['attributes'] for mem in basic_memories]
                
                self.memory_client.memory_service.db_manager.add_user_profiles(
                    user_id=self.persona_user_id,
                    profiles=profiles,
                    attributes=attributes,
                    project_id=self.config.get('project_id', 'default')
                )
                
                logger.info(f"🧠 为虚拟人初始化了 {len(basic_memories)} 条基础记忆")
            
        except Exception as e:
            logger.error(f"❌ 初始化虚拟人记忆失败: {e}")

    def add_persona_memory(self, memory_type: str, category: str, title: str, content: str,
                          emotion: str = None, importance: float = 1.0, keywords: str = None,
                          time_period: str = 'recent', sharing_level: int = 1) -> str:
        """
        添加个人记忆到本地数据库

        Args:
            memory_type: 记忆类型
            category: 分类
            title: 标题
            content: 内容
            emotion: 情感
            importance: 重要性
            keywords: 关键词
            time_period: 时间段
            sharing_level: 分享级别

        Returns:
            记忆ID
        """
        try:
            # 获取虚拟人用户
            persona_user = self.memory_client.get_or_create_user(self.persona_user_id)
            
            # 构造记忆消息
            messages = [
                {
                    "role": "assistant",
                    "content": f"记忆类型: {memory_type}, 分类: {category}, 标题: {title}, 情感: {emotion}, 重要性: {importance}, 时间段: {time_period}, 分享级别: {sharing_level}"
                },
                {
                    "role": "assistant",
                    "content": content,
                    "timestamp": datetime.now().isoformat()
                }
            ]
            
            # 插入到本地数据库
            blob_id = self.memory_client.insert_chat_data(persona_user, messages)
            
            # 如果配置了自动刷新，立即刷新记忆
            if self.config.get('auto_flush', True):
                self.memory_client.flush_user_memory(persona_user)
            
            logger.info(f"💾 虚拟人记忆已保存 - 类型: {memory_type}, 标题: {title}")
            return blob_id
            
        except Exception as e:
            logger.error(f"💥 添加虚拟人记忆失败: {e}")
            return ""

    def get_relevant_persona_memories(self, context: str, user_affection: int = 50,
                                    limit: int = 3) -> List[Dict]:
        """
        根据对话上下文获取相关的个人记忆

        Args:
            context: 对话上下文
            user_affection: 用户好感度，影响分享私密记忆的概率
            limit: 返回记忆数量限制

        Returns:
            相关记忆列表
        """
        try:
            # 获取虚拟人用户
            persona_user = self.memory_client.get_or_create_user(self.persona_user_id)
            
            # 获取用户画像作为记忆
            profile = self.memory_client.get_user_profile(persona_user, need_json=True)
            
            if not profile:
                logger.warning(f"⚠️ 虚拟人 {self.persona_user_id} 没有找到记忆")
                return []

            # 转换为记忆格式
            memories = []
            for item in profile[:limit]:  # 限制数量
                if isinstance(item, dict) and 'content' in item:
                    attributes = item.get('attributes', {})
                    sharing_level = attributes.get('sharing_level', 1)
                    
                    # 根据好感度过滤私密记忆
                    if sharing_level > 1 and user_affection < sharing_level * 20:
                        continue
                    
                    memories.append({
                        'content': item['content'],
                        'type': attributes.get('topic', 'personal'),
                        'category': attributes.get('sub_topic', ''),
                        'emotion': attributes.get('emotion', 'neutral'),
                        'importance': attributes.get('importance', 1.0),
                        'sharing_level': sharing_level,
                        'created_at': item.get('created_at', datetime.now().isoformat())
                    })

            logger.info(f"🔍 获取虚拟人记忆 - 上下文: {context[:30]}..., 结果: {len(memories)} 条")
            return memories
            
        except Exception as e:
            logger.error(f"💥 获取虚拟人记忆失败: {e}")
            return []

    def get_persona_context(self, max_token_size: int = None, prefer_topics: List[str] = None,
                           chats: List[Dict] = None, optimize_tokens: bool = False) -> str:
        """
        获取虚拟人上下文

        Args:
            max_token_size: 最大token数量
            prefer_topics: 优先话题
            chats: 聊天历史
            optimize_tokens: 是否优化token使用

        Returns:
            格式化的虚拟人上下文字符串
        """
        try:
            # 获取虚拟人用户
            persona_user = self.memory_client.get_or_create_user(self.persona_user_id)

            # 使用配置的默认值，但支持token优化
            default_token_size = self.config.get('max_token_size', 500)
            if optimize_tokens:
                # 根据对话长度动态调整token大小
                if chats and len(chats) > 10:
                    default_token_size = min(default_token_size, 300)
                elif chats and len(chats) > 5:
                    default_token_size = min(default_token_size, 400)

            max_token_size = max_token_size or default_token_size
            prefer_topics = prefer_topics or ['basic_info', 'personality', 'interests_hobbies', 'speaking_style']

            # 获取本地生成的上下文，传递聊天历史以提高相关性
            context = self.memory_client.get_user_context(
                persona_user,
                max_token_size=max_token_size,
                prefer_topics=prefer_topics,
                chats=chats,
                optimize_tokens=optimize_tokens
            )

            return context
            
        except Exception as e:
            logger.error(f"💥 获取虚拟人上下文失败: {e}")
            return ""

    def get_memory_stats(self) -> Dict:
        """
        获取虚拟人记忆统计信息

        Returns:
            记忆统计信息
        """
        try:
            # 获取虚拟人用户
            persona_user = self.memory_client.get_or_create_user(self.persona_user_id)
            
            # 获取用户画像
            profile = self.memory_client.get_user_profile(persona_user, need_json=True)
            
            # 统计信息
            total_memories = len(profile) if profile else 0
            
            return {
                'total_memories': total_memories,
                'persona_name': self.persona_config['name'],
                'provider': 'local_memory',
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"💥 获取虚拟人记忆统计失败: {e}")
            return {
                'total_memories': 0,
                'persona_name': self.persona_config['name'],
                'provider': 'local_memory',
                'error': str(e)
            }

    def flush_persona_memory(self) -> bool:
        """刷新虚拟人记忆"""
        try:
            persona_user = self.memory_client.get_or_create_user(self.persona_user_id)
            return self.memory_client.flush_user_memory(persona_user)
        except Exception as e:
            logger.error(f"💥 刷新虚拟人记忆失败: {e}")
            return False


# 兼容性别名
MemobasePersonaMemoryManager = LocalPersonaMemoryManager
