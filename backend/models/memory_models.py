"""
本地记忆模块数据库模型
基于memobase设计，简化实现，直连PostgreSQL
"""

from sqlalchemy import create_engine, Column, String, Text, Integer, Float, DateTime, JSON, Boolean, ForeignKey, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from datetime import datetime
import uuid
from typing import Optional, Dict, List, Any
from pydantic import BaseModel, Field
import os

Base = declarative_base()

# 数据库连接配置
DATABASE_URL = os.getenv('MEMORY_DATABASE_URL', 'sqlite:///memory.db')

class User(Base):
    """用户表"""
    __tablename__ = 'memory_users'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(String(255), nullable=False, unique=True, index=True)  # 外部用户ID
    project_id = Column(String(64), nullable=False, default='default')
    additional_fields = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    blobs = relationship("MemoryBlob", back_populates="user", cascade="all, delete-orphan")
    profiles = relationship("UserProfile", back_populates="user", cascade="all, delete-orphan")
    events = relationship("UserEvent", back_populates="user", cascade="all, delete-orphan")
    
    __table_args__ = (
        Index('idx_user_project', 'user_id', 'project_id'),
    )


class MemoryBlob(Base):
    """记忆数据块表"""
    __tablename__ = 'memory_blobs'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_uuid = Column(UUID(as_uuid=True), ForeignKey('memory_users.id'), nullable=False)
    blob_type = Column(String(50), nullable=False)  # chat, doc, image, code, transcript
    blob_data = Column(JSON, nullable=False)
    token_size = Column(Integer, nullable=True)
    processed = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="blobs")
    
    __table_args__ = (
        Index('idx_blob_user_type', 'user_uuid', 'blob_type'),
        Index('idx_blob_created', 'created_at'),
    )


class UserProfile(Base):
    """用户画像表"""
    __tablename__ = 'user_profiles'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_uuid = Column(UUID(as_uuid=True), ForeignKey('memory_users.id'), nullable=False)
    content = Column(Text, nullable=False)
    attributes = Column(JSON, nullable=True)  # topic, sub_topic等
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="profiles")
    
    __table_args__ = (
        Index('idx_profile_user', 'user_uuid'),
        Index('idx_profile_updated', 'updated_at'),
    )


class UserEvent(Base):
    """用户事件表"""
    __tablename__ = 'user_events'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_uuid = Column(UUID(as_uuid=True), ForeignKey('memory_users.id'), nullable=False)
    event_data = Column(JSON, nullable=False)
    embedding = Column(ARRAY(Float), nullable=True)  # 向量嵌入
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="events")
    
    __table_args__ = (
        Index('idx_event_user', 'user_uuid'),
        Index('idx_event_created', 'created_at'),
    )


# Pydantic模型用于数据验证和序列化
class ProfileDelta(BaseModel):
    """画像增量"""
    content: str = Field(..., description="画像内容")
    attributes: Optional[Dict] = Field(None, description="画像属性，包含topic, sub_topic")


class EventTag(BaseModel):
    """事件标签"""
    tag: str = Field(..., description="标签名")
    value: str = Field(..., description="标签值")


class EventData(BaseModel):
    """事件数据"""
    profile_delta: List[ProfileDelta] = Field(default_factory=list, description="画像增量列表")
    event_tip: Optional[str] = Field(None, description="事件提示")
    event_tags: Optional[List[EventTag]] = Field(None, description="事件标签列表")


class OpenAICompatibleMessage(BaseModel):
    """OpenAI兼容的消息格式"""
    role: str = Field(..., description="角色: user, assistant, system")
    content: str = Field(..., description="消息内容")
    alias: Optional[str] = Field(None, description="别名")
    created_at: Optional[str] = Field(None, description="创建时间")


class BlobType:
    """Blob类型枚举"""
    CHAT = "chat"
    DOC = "doc"
    IMAGE = "image"
    CODE = "code"
    TRANSCRIPT = "transcript"


# 数据库引擎和会话
engine = None
SessionLocal = None

def init_database(database_url: str = None):
    """初始化数据库连接"""
    global engine, SessionLocal
    
    url = database_url or DATABASE_URL
    engine = create_engine(url, echo=False)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    # 创建所有表
    Base.metadata.create_all(bind=engine)


def get_db_session():
    """获取数据库会话"""
    if SessionLocal is None:
        raise RuntimeError("数据库未初始化，请先调用 init_database()")
    
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, database_url: str = None):
        if engine is None:
            init_database(database_url)
    
    def get_session(self):
        """获取数据库会话"""
        return SessionLocal()
    
    def create_user(self, user_id: str, project_id: str = 'default', additional_fields: Dict = None) -> User:
        """创建或获取用户"""
        with self.get_session() as session:
            # 先尝试获取现有用户
            user = session.query(User).filter_by(user_id=user_id, project_id=project_id).first()
            if user:
                return user
            
            # 创建新用户
            user = User(
                user_id=user_id,
                project_id=project_id,
                additional_fields=additional_fields or {}
            )
            session.add(user)
            session.commit()
            session.refresh(user)
            return user
    
    def get_user(self, user_id: str, project_id: str = 'default') -> Optional[User]:
        """获取用户"""
        with self.get_session() as session:
            return session.query(User).filter_by(user_id=user_id, project_id=project_id).first()

    def insert_blob(self, user_id: str, blob_type: str, blob_data: Dict, project_id: str = 'default') -> str:
        """插入记忆数据块"""
        with self.get_session() as session:
            user = self.create_user(user_id, project_id)

            blob = MemoryBlob(
                user_uuid=user.id,
                blob_type=blob_type,
                blob_data=blob_data,
                token_size=self._estimate_token_size(blob_data)
            )
            session.add(blob)
            session.commit()
            session.refresh(blob)
            return str(blob.id)

    def get_user_blobs(self, user_id: str, blob_type: str = None, project_id: str = 'default',
                      limit: int = 10, offset: int = 0) -> List[MemoryBlob]:
        """获取用户的记忆数据块"""
        with self.get_session() as session:
            user = self.get_user(user_id, project_id)
            if not user:
                return []

            query = session.query(MemoryBlob).filter_by(user_uuid=user.id)
            if blob_type:
                query = query.filter_by(blob_type=blob_type)

            return query.order_by(MemoryBlob.created_at.desc()).offset(offset).limit(limit).all()

    def add_user_profiles(self, user_id: str, profiles: List[str], attributes: List[Dict],
                         project_id: str = 'default') -> List[str]:
        """添加用户画像"""
        with self.get_session() as session:
            user = self.create_user(user_id, project_id)

            profile_ids = []
            for content, attr in zip(profiles, attributes):
                profile = UserProfile(
                    user_uuid=user.id,
                    content=content,
                    attributes=attr
                )
                session.add(profile)
                session.flush()
                profile_ids.append(str(profile.id))

            session.commit()
            return profile_ids

    def get_user_profiles(self, user_id: str, project_id: str = 'default',
                         limit: int = 50) -> List[UserProfile]:
        """获取用户画像"""
        with self.get_session() as session:
            user = self.get_user(user_id, project_id)
            if not user:
                return []

            return session.query(UserProfile).filter_by(user_uuid=user.id)\
                         .order_by(UserProfile.updated_at.desc()).limit(limit).all()

    def add_user_event(self, user_id: str, event_data: Dict, project_id: str = 'default') -> str:
        """添加用户事件"""
        with self.get_session() as session:
            user = self.create_user(user_id, project_id)

            event = UserEvent(
                user_uuid=user.id,
                event_data=event_data
            )
            session.add(event)
            session.commit()
            session.refresh(event)
            return str(event.id)

    def get_user_events(self, user_id: str, project_id: str = 'default',
                       limit: int = 10) -> List[UserEvent]:
        """获取用户事件"""
        with self.get_session() as session:
            user = self.get_user(user_id, project_id)
            if not user:
                return []

            return session.query(UserEvent).filter_by(user_uuid=user.id)\
                         .order_by(UserEvent.created_at.desc()).limit(limit).all()

    def _estimate_token_size(self, blob_data: Dict) -> int:
        """估算token大小"""
        import json
        text = json.dumps(blob_data, ensure_ascii=False)
        # 粗略估算：中文约3字符=1token，英文约4字符=1token
        return len(text) // 3
