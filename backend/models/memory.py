"""
简化的本地记忆模块
直连PostgreSQL，无复杂依赖
"""

import os
import json
import uuid
import psycopg2
import psycopg2.extras
from datetime import datetime
from typing import Optional, Dict, List, Any
from contextlib import contextmanager

# PostgreSQL数据库连接配置
DATABASE_URL = os.getenv('MEMORY_DATABASE_URL', 'postgresql://postgres:123456@localhost:35432/virtual_memory')


class MemoryDatabase:
    """简化的记忆数据库管理器"""
    
    def __init__(self, database_url: str = None):
        self.database_url = database_url or DATABASE_URL
        self.init_database()
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接"""
        conn = psycopg2.connect(self.database_url)
        try:
            yield conn
        finally:
            conn.close()
    
    def init_database(self):
        """初始化数据库表结构"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 创建用户表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    user_id VARCHAR(255) UNIQUE NOT NULL,
                    name VARCHAR(255),
                    additional_data JSONB DEFAULT '{}',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建记忆数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS memories (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    user_id VARCHAR(255) NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
                    content TEXT NOT NULL,
                    memory_type VARCHAR(100) DEFAULT 'chat',
                    attributes JSONB DEFAULT '{}',
                    importance FLOAT DEFAULT 1.0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建用户画像表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_profiles (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    user_id VARCHAR(255) NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
                    topic VARCHAR(100) NOT NULL,
                    content TEXT NOT NULL,
                    confidence FLOAT DEFAULT 1.0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建对话记录表（迁移自SQLite）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS conversations (
                    id SERIAL PRIMARY KEY,
                    user_id VARCHAR(255) NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
                    message TEXT NOT NULL,
                    sender VARCHAR(20) NOT NULL,
                    emotion_score FLOAT DEFAULT 0.0,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建好感度记录表（迁移自SQLite）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS affection_levels (
                    id SERIAL PRIMARY KEY,
                    user_id VARCHAR(255) NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
                    level INTEGER NOT NULL,
                    change_reason TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建虚拟人状态表（迁移自SQLite）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS persona_states (
                    id SERIAL PRIMARY KEY,
                    date DATE NOT NULL,
                    current_activity TEXT,
                    mood TEXT,
                    work_content TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_memories_user_id ON memories(user_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_memories_type ON memories(memory_type)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_profiles_user_id ON user_profiles(user_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_profiles_topic ON user_profiles(topic)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_affection_user_id ON affection_levels(user_id)')
            
            conn.commit()
    
    def create_user(self, user_id: str, name: str = None, additional_data: Dict = None) -> Dict:
        """创建或获取用户"""
        with self.get_connection() as conn:
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            # 尝试获取现有用户
            cursor.execute('SELECT * FROM users WHERE user_id = %s', (user_id,))
            user = cursor.fetchone()
            
            if user:
                return dict(user)
            
            # 创建新用户
            cursor.execute('''
                INSERT INTO users (user_id, name, additional_data)
                VALUES (%s, %s, %s)
                RETURNING *
            ''', (user_id, name or f'用户{user_id[:8]}', json.dumps(additional_data or {})))
            
            user = cursor.fetchone()
            conn.commit()
            return dict(user)
    
    def add_memory(self, user_id: str, content: str, memory_type: str = 'chat', 
                   attributes: Dict = None, importance: float = 1.0) -> str:
        """添加记忆"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO memories (user_id, content, memory_type, attributes, importance)
                VALUES (%s, %s, %s, %s, %s)
                RETURNING id
            ''', (user_id, content, memory_type, json.dumps(attributes or {}), importance))
            
            memory_id = cursor.fetchone()[0]
            conn.commit()
            return str(memory_id)
    
    def get_memories(self, user_id: str, memory_type: str = None, limit: int = 10) -> List[Dict]:
        """获取用户记忆"""
        with self.get_connection() as conn:
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            if memory_type:
                cursor.execute('''
                    SELECT * FROM memories 
                    WHERE user_id = %s AND memory_type = %s
                    ORDER BY created_at DESC LIMIT %s
                ''', (user_id, memory_type, limit))
            else:
                cursor.execute('''
                    SELECT * FROM memories 
                    WHERE user_id = %s
                    ORDER BY created_at DESC LIMIT %s
                ''', (user_id, limit))
            
            return [dict(row) for row in cursor.fetchall()]
    
    def add_user_profile(self, user_id: str, topic: str, content: str, confidence: float = 1.0) -> str:
        """添加用户画像"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO user_profiles (user_id, topic, content, confidence)
                VALUES (%s, %s, %s, %s)
                RETURNING id
            ''', (user_id, topic, content, confidence))
            
            profile_id = cursor.fetchone()[0]
            conn.commit()
            return str(profile_id)
    
    def get_user_profiles(self, user_id: str, topic: str = None, limit: int = 20) -> List[Dict]:
        """获取用户画像"""
        with self.get_connection() as conn:
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            if topic:
                cursor.execute('''
                    SELECT * FROM user_profiles 
                    WHERE user_id = %s AND topic = %s
                    ORDER BY updated_at DESC LIMIT %s
                ''', (user_id, topic, limit))
            else:
                cursor.execute('''
                    SELECT * FROM user_profiles 
                    WHERE user_id = %s
                    ORDER BY updated_at DESC LIMIT %s
                ''', (user_id, limit))
            
            return [dict(row) for row in cursor.fetchall()]
    
    def save_conversation(self, user_id: str, message: str, sender: str, emotion_score: float = 0.0):
        """保存对话记录"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO conversations (user_id, message, sender, emotion_score)
                VALUES (%s, %s, %s, %s)
            ''', (user_id, message, sender, emotion_score))
            
            conn.commit()
    
    def get_recent_conversations(self, user_id: str, limit: int = 10) -> List[Dict]:
        """获取最近对话"""
        with self.get_connection() as conn:
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            cursor.execute('''
                SELECT * FROM conversations 
                WHERE user_id = %s
                ORDER BY timestamp DESC LIMIT %s
            ''', (user_id, limit))
            
            return [dict(row) for row in cursor.fetchall()]
    
    def update_affection(self, user_id: str, change: int, reason: str) -> int:
        """更新好感度"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 获取当前好感度
            cursor.execute('''
                SELECT level FROM affection_levels 
                WHERE user_id = %s 
                ORDER BY timestamp DESC LIMIT 1
            ''', (user_id,))
            
            result = cursor.fetchone()
            current_level = result[0] if result else 10  # 默认初始好感度
            
            # 计算新好感度
            new_level = max(0, min(100, current_level + change))
            
            # 记录好感度变化
            cursor.execute('''
                INSERT INTO affection_levels (user_id, level, change_reason)
                VALUES (%s, %s, %s)
            ''', (user_id, new_level, reason))
            
            conn.commit()
            return new_level
    
    def get_current_affection(self, user_id: str) -> int:
        """获取当前好感度"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT level FROM affection_levels 
                WHERE user_id = %s 
                ORDER BY timestamp DESC LIMIT 1
            ''', (user_id,))
            
            result = cursor.fetchone()
            return result[0] if result else 10  # 默认初始好感度


# 全局数据库实例
memory_db = None

def get_memory_db() -> MemoryDatabase:
    """获取记忆数据库实例"""
    global memory_db
    if memory_db is None:
        memory_db = MemoryDatabase()
    return memory_db
