# 本地记忆系统

本项目已从远程Memobase服务切换到本地PostgreSQL记忆系统，提供更简单、可控的记忆管理功能。

## 系统架构

### 核心组件

1. **数据库模型** (`models/memory_models.py`)
   - 基于SQLAlchemy ORM
   - PostgreSQL数据库
   - 用户、记忆数据块、用户画像、用户事件表

2. **记忆服务** (`services/local_memory_service.py`)
   - 核心记忆处理逻辑
   - 聊天数据存储和检索
   - 用户画像生成和管理

3. **记忆客户端** (`clients/local_memory_client.py`)
   - 兼容原有Memobase客户端接口
   - 提供统一的API调用方式

4. **记忆管理器**
   - `services/local_memory_manager.py` - 用户记忆管理
   - `services/local_persona_memory_manager.py` - 虚拟人记忆管理

## 数据库表结构

### memory_users (用户表)
- `id` - UUID主键
- `user_id` - 外部用户ID
- `project_id` - 项目ID
- `additional_fields` - 额外字段(JSON)
- `created_at`, `updated_at` - 时间戳

### memory_blobs (记忆数据块表)
- `id` - UUID主键
- `user_uuid` - 用户外键
- `blob_type` - 数据类型(chat, doc, image等)
- `blob_data` - 数据内容(JSON)
- `token_size` - Token大小
- `processed` - 是否已处理

### user_profiles (用户画像表)
- `id` - UUID主键
- `user_uuid` - 用户外键
- `content` - 画像内容
- `attributes` - 画像属性(JSON)

### user_events (用户事件表)
- `id` - UUID主键
- `user_uuid` - 用户外键
- `event_data` - 事件数据(JSON)
- `embedding` - 向量嵌入(可选)

## 安装和配置

### 1. 安装依赖

```bash
cd backend
pip install -r requirements.txt
```

### 2. 配置PostgreSQL

确保PostgreSQL服务已启动，并创建数据库：

```sql
CREATE DATABASE virtual_memory;
```

### 3. 配置环境变量

在项目根目录的`.env`文件中添加：

```env
# 本地记忆系统配置
USE_LOCAL_MEMORY=true
MEMORY_DATABASE_URL=postgresql://username:password@localhost:5432/virtual_memory
MEMORY_PROJECT_ID=virtual_companion
MEMORY_MAX_TOKEN_SIZE=500
MEMORY_AUTO_FLUSH=true
```

### 4. 初始化数据库

```bash
cd backend
python init_memory_database.py
```

### 5. 测试系统

```bash
cd backend
python switch_to_local_memory.py
```

## 使用方法

### 基本用法

```python
from clients.local_memory_client import LocalMemoryClient, ChatBlob

# 创建客户端
client = LocalMemoryClient()

# 获取或创建用户
user = client.get_or_create_user("user123")

# 插入聊天数据
chat_blob = ChatBlob(messages=[
    {"role": "user", "content": "你好"}
])
blob_id = user.insert(chat_blob)

# 刷新记忆
user.flush()

# 获取用户画像
profile = user.get_profile()

# 获取用户上下文
context = user.get_context(max_token_size=500)
```

### 记忆管理器用法

```python
from services.local_memory_manager import LocalMemoryManager

# 创建记忆管理器
memory_manager = LocalMemoryManager()

# 提取记忆
memories = memory_manager.extract_memories_from_text("user123", "我喜欢画画")

# 获取相关记忆
relevant = memory_manager.get_relevant_memories("user123", "艺术相关")

# 获取用户上下文
context = memory_manager.get_user_context("user123")
```

### 虚拟人记忆管理

```python
from services.local_persona_memory_manager import LocalPersonaMemoryManager

# 创建虚拟人记忆管理器
persona_manager = LocalPersonaMemoryManager()

# 添加个人记忆
memory_id = persona_manager.add_persona_memory(
    memory_type="experience",
    category="travel",
    title="上海旅行",
    content="去年夏天我去了上海，看了很多美术馆"
)

# 获取相关记忆
memories = persona_manager.get_relevant_persona_memories("旅行")

# 获取虚拟人上下文
context = persona_manager.get_persona_context()
```

## 特性

### 1. 兼容性
- 保持与原有Memobase接口的兼容性
- 无需修改现有业务代码
- 平滑迁移

### 2. 性能优化
- 本地数据库，无网络延迟
- 索引优化，快速查询
- Token使用优化

### 3. 数据安全
- 本地存储，数据可控
- 支持备份和恢复
- 无外部依赖

### 4. 扩展性
- 模块化设计
- 易于扩展新功能
- 支持自定义处理逻辑

## 迁移说明

### 从Memobase迁移

1. **保留原有配置** - 原有的Memobase配置仍然保留，便于回滚
2. **数据格式兼容** - 新系统支持原有的数据格式
3. **API兼容** - 保持相同的API接口
4. **渐进式迁移** - 可以逐步切换各个模块

### 配置切换

在`config.py`中，系统会优先使用`MEMORY_CONFIG`，如果未配置则回退到`MEMOBASE_CONFIG`。

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查PostgreSQL服务状态
   - 验证数据库URL配置
   - 确认用户权限

2. **依赖包缺失**
   ```bash
   pip install sqlalchemy psycopg2-binary pydantic
   ```

3. **表结构问题**
   ```bash
   python init_memory_database.py --force
   ```

### 日志调试

启用详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 性能监控

查看数据库统计：

```python
from clients.local_memory_client import LocalMemoryClient
client = LocalMemoryClient()
stats = client.get_usage_stats()
print(stats)
```

## 开发指南

### 添加新功能

1. 在`models/memory_models.py`中添加新的数据模型
2. 在`services/local_memory_service.py`中实现业务逻辑
3. 在`clients/local_memory_client.py`中暴露API接口
4. 更新相应的管理器类

### 测试

```bash
# 运行初始化测试
python init_memory_database.py

# 运行系统测试
python switch_to_local_memory.py

# 启动系统测试
python ../start.py
```

## 技术栈

- **数据库**: PostgreSQL
- **ORM**: SQLAlchemy
- **数据验证**: Pydantic
- **向量支持**: PostgreSQL数组类型
- **连接池**: SQLAlchemy连接池
- **日志**: Python logging模块
